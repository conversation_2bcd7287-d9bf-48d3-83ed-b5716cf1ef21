import { NextRequest, NextResponse } from 'next/server'
import { createServerSupabaseClient } from '@/lib/supabase/server'

export async function POST(
  request: NextRequest,
  { params }: { params: Promise<{ commentId: string }> }
) {
  try {
    const { commentId } = await params
    const supabase = await createServerSupabaseClient()
    const { data: { user }, error: authError } = await supabase.auth.getUser()

    if (authError || !user) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 })
    }

    const { reactionType } = await request.json()

    if (!reactionType) {
      return NextResponse.json({ error: 'Reaction type is required' }, { status: 400 })
    }

    // Check if comment exists and user has access to it
    const { data: comment, error: commentError } = await supabase
      .from('comments')
      .select(`
        id,
        book_id,
        diary_entry_id
      `)
      .eq('id', commentId)
      .single()

    if (commentError || !comment) {
      return NextResponse.json({ error: 'Comment not found' }, { status: 404 })
    }

    // Check if user already has a reaction on this comment
    const { data: existingReaction } = await supabase
      .from('reactions')
      .select('reaction_type')
      .eq('comment_id', commentId)
      .eq('user_id', user.id)
      .single()

    if (existingReaction) {
      if (existingReaction.reaction_type === reactionType) {
        // Remove reaction if it's the same
        const { error } = await supabase
          .from('reactions')
          .delete()
          .eq('comment_id', commentId)
          .eq('user_id', user.id)

        if (error) {
          console.error('Error removing reaction:', error)
          return NextResponse.json({ error: 'Failed to remove reaction' }, { status: 500 })
        }

        return NextResponse.json({ reactionType: null })
      } else {
        // Update reaction if it's different
        const { error } = await supabase
          .from('reactions')
          .update({ 
            reaction_type: reactionType,
            updated_at: new Date().toISOString()
          })
          .eq('comment_id', commentId)
          .eq('user_id', user.id)

        if (error) {
          console.error('Error updating reaction:', error)
          return NextResponse.json({ error: 'Failed to update reaction' }, { status: 500 })
        }

        return NextResponse.json({ reactionType })
      }
    } else {
      // Add new reaction
      const { error } = await supabase
        .from('reactions')
        .insert({
          user_id: user.id,
          comment_id: commentId,
          reaction_type: reactionType
        })

      if (error) {
        console.error('Error adding reaction:', error)
        return NextResponse.json({ error: 'Failed to add reaction' }, { status: 500 })
      }

      return NextResponse.json({ reactionType })
    }
  } catch (error) {
    console.error('Comment reaction API error:', error)
    return NextResponse.json({ error: 'Internal server error' }, { status: 500 })
  }
}

export async function GET(
  request: NextRequest,
  { params }: { params: Promise<{ commentId: string }> }
) {
  try {
    const { commentId } = await params
    const supabase = await createServerSupabaseClient()
    const { data: { user } } = await supabase.auth.getUser()

    // Get reaction counts for this comment
    const { data: reactions, error: reactionsError } = await supabase
      .from('reactions')
      .select('reaction_type')
      .eq('comment_id', commentId)

    if (reactionsError) {
      console.error('Error fetching reactions:', reactionsError)
      return NextResponse.json({ error: 'Failed to fetch reactions' }, { status: 500 })
    }

    // Count reactions by type
    const reactionCounts: Record<string, number> = {}
    reactions?.forEach(reaction => {
      reactionCounts[reaction.reaction_type] = (reactionCounts[reaction.reaction_type] || 0) + 1
    })

    // Get user's reaction if logged in
    let userReaction = null
    if (user) {
      const { data: userReactionData } = await supabase
        .from('reactions')
        .select('reaction_type')
        .eq('user_id', user.id)
        .eq('comment_id', commentId)
        .single()

      userReaction = userReactionData?.reaction_type || null
    }

    return NextResponse.json({ 
      reactions: reactionCounts,
      userReaction 
    })
  } catch (error) {
    console.error('Comment reaction GET API error:', error)
    return NextResponse.json({ error: 'Internal server error' }, { status: 500 })
  }
}
